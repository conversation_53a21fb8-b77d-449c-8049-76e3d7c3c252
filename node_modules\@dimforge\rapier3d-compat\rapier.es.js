let A;const I=new Array(128).fill(void 0);I.push(void 0,null,!0,!1);let g=I.length;function C(A){g===I.length&&I.push(I.length+1);const C=g;return g=I[C],I[C]=A,C}function B(A){return I[A]}function Q(A){const C=B(A);return function(A){A<132||(I[A]=g,g=A)}(A),C}function E(A){return null==A}let i=null;function D(){return null!==i&&0!==i.byteLength||(i=new Float64Array(A.memory.buffer)),i}let o=null;function G(){return null!==o&&0!==o.byteLength||(o=new Int32Array(A.memory.buffer)),o}const w="undefined"!=typeof TextDecoder?new TextDecoder("utf-8",{ignoreBOM:!0,fatal:!0}):{decode:()=>{throw Error("TextDecoder not available")}};"undefined"!=typeof TextDecoder&&w.decode();let S=null;function k(I,g){return I>>>=0,w.decode((null!==S&&0!==S.byteLength||(S=new Uint8Array(A.memory.buffer)),S).subarray(I,I+g))}function a(A,I){if(!(A instanceof I))throw new Error(`expected instance of ${I.name}`);return A.ptr}let h=null;function K(){return null!==h&&0!==h.byteLength||(h=new Float32Array(A.memory.buffer)),h}let U=128;function J(A){if(1==U)throw new Error("out of js stack");return I[--U]=A,U}function N(A,I){return A>>>=0,K().subarray(A/4,A/4+I)}let y=null;function M(){return null!==y&&0!==y.byteLength||(y=new Uint32Array(A.memory.buffer)),y}let F=0;function q(A,I){const g=I(4*A.length,4)>>>0;return K().set(A,g/4),F=A.length,g}function R(A,I){const g=I(4*A.length,4)>>>0;return M().set(A,g/4),F=A.length,g}function s(I,g){try{return I.apply(this,g)}catch(I){A.__wbindgen_exn_store(C(I))}}Object.freeze({Dynamic:0,0:"Dynamic",Fixed:1,1:"Fixed",KinematicPositionBased:2,2:"KinematicPositionBased",KinematicVelocityBased:3,3:"KinematicVelocityBased"}),Object.freeze({Vertex:0,0:"Vertex",Edge:1,1:"Edge",Face:2,2:"Face",Unknown:3,3:"Unknown"}),Object.freeze({AccelerationBased:0,0:"AccelerationBased",ForceBased:1,1:"ForceBased"});const c=Object.freeze({Ball:0,0:"Ball",Cuboid:1,1:"Cuboid",Capsule:2,2:"Capsule",Segment:3,3:"Segment",Polyline:4,4:"Polyline",Triangle:5,5:"Triangle",TriMesh:6,6:"TriMesh",HeightField:7,7:"HeightField",Compound:8,8:"Compound",ConvexPolyhedron:9,9:"ConvexPolyhedron",Cylinder:10,10:"Cylinder",Cone:11,11:"Cone",RoundCuboid:12,12:"RoundCuboid",RoundTriangle:13,13:"RoundTriangle",RoundCylinder:14,14:"RoundCylinder",RoundCone:15,15:"RoundCone",RoundConvexPolyhedron:16,16:"RoundConvexPolyhedron",HalfSpace:17,17:"HalfSpace"}),Y=Object.freeze({X:0,0:"X",Y:1,1:"Y",Z:2,2:"Z",AngX:3,3:"AngX",AngY:4,4:"AngY",AngZ:5,5:"AngZ"}),H=Object.freeze({Revolute:0,0:"Revolute",Fixed:1,1:"Fixed",Prismatic:2,2:"Prismatic",Rope:3,3:"Rope",Spring:4,4:"Spring",Spherical:5,5:"Spherical",Generic:6,6:"Generic"});class l{static __wrap(A){A>>>=0;const I=Object.create(l.prototype);return I.__wbg_ptr=A,I}__destroy_into_raw(){const A=this.__wbg_ptr;return this.__wbg_ptr=0,A}free(){const I=this.__destroy_into_raw();A.__wbg_rawbroadphase_free(I)}constructor(){const I=A.rawbroadphase_new();return this.__wbg_ptr=I>>>0,this}}class L{__destroy_into_raw(){const A=this.__wbg_ptr;return this.__wbg_ptr=0,A}free(){const I=this.__destroy_into_raw();A.__wbg_rawccdsolver_free(I)}constructor(){const I=A.rawccdsolver_new();return this.__wbg_ptr=I>>>0,this}}class t{__destroy_into_raw(){const A=this.__wbg_ptr;return this.__wbg_ptr=0,A}free(){const I=this.__destroy_into_raw();A.__wbg_rawcharactercollision_free(I)}constructor(){const I=A.rawcharactercollision_new();return this.__wbg_ptr=I>>>0,this}handle(){return A.rawcharactercollision_handle(this.__wbg_ptr)}translationDeltaApplied(){const I=A.rawcharactercollision_translationDeltaApplied(this.__wbg_ptr);return DA.__wrap(I)}translationDeltaRemaining(){const I=A.rawcharactercollision_translationDeltaRemaining(this.__wbg_ptr);return DA.__wrap(I)}toi(){return A.rawcharactercollision_toi(this.__wbg_ptr)}worldWitness1(){const I=A.rawcharactercollision_worldWitness1(this.__wbg_ptr);return DA.__wrap(I)}worldWitness2(){const I=A.rawcharactercollision_worldWitness2(this.__wbg_ptr);return DA.__wrap(I)}worldNormal1(){const I=A.rawcharactercollision_worldNormal1(this.__wbg_ptr);return DA.__wrap(I)}worldNormal2(){const I=A.rawcharactercollision_worldNormal2(this.__wbg_ptr);return DA.__wrap(I)}}class p{static __wrap(A){A>>>=0;const I=Object.create(p.prototype);return I.__wbg_ptr=A,I}__destroy_into_raw(){const A=this.__wbg_ptr;return this.__wbg_ptr=0,A}free(){const I=this.__destroy_into_raw();A.__wbg_rawcolliderset_free(I)}coTranslation(I){const g=A.rawcolliderset_coTranslation(this.__wbg_ptr,I);return DA.__wrap(g)}coRotation(I){const g=A.rawcolliderset_coRotation(this.__wbg_ptr,I);return IA.__wrap(g)}coSetTranslation(I,g,C,B){A.rawcolliderset_coSetTranslation(this.__wbg_ptr,I,g,C,B)}coSetTranslationWrtParent(I,g,C,B){A.rawcolliderset_coSetTranslationWrtParent(this.__wbg_ptr,I,g,C,B)}coSetRotation(I,g,C,B,Q){A.rawcolliderset_coSetRotation(this.__wbg_ptr,I,g,C,B,Q)}coSetRotationWrtParent(I,g,C,B,Q){A.rawcolliderset_coSetRotationWrtParent(this.__wbg_ptr,I,g,C,B,Q)}coIsSensor(I){return 0!==A.rawcolliderset_coIsSensor(this.__wbg_ptr,I)}coShapeType(I){return A.rawcolliderset_coShapeType(this.__wbg_ptr,I)}coHalfspaceNormal(I){const g=A.rawcolliderset_coHalfspaceNormal(this.__wbg_ptr,I);return 0===g?void 0:DA.__wrap(g)}coHalfExtents(I){const g=A.rawcolliderset_coHalfExtents(this.__wbg_ptr,I);return 0===g?void 0:DA.__wrap(g)}coSetHalfExtents(I,g){a(g,DA),A.rawcolliderset_coSetHalfExtents(this.__wbg_ptr,I,g.__wbg_ptr)}coRadius(I){try{const B=A.__wbindgen_add_to_stack_pointer(-16);A.rawcolliderset_coRadius(B,this.__wbg_ptr,I);var g=G()[B/4+0],C=K()[B/4+1];return 0===g?void 0:C}finally{A.__wbindgen_add_to_stack_pointer(16)}}coSetRadius(I,g){A.rawcolliderset_coSetRadius(this.__wbg_ptr,I,g)}coHalfHeight(I){try{const B=A.__wbindgen_add_to_stack_pointer(-16);A.rawcolliderset_coHalfHeight(B,this.__wbg_ptr,I);var g=G()[B/4+0],C=K()[B/4+1];return 0===g?void 0:C}finally{A.__wbindgen_add_to_stack_pointer(16)}}coSetHalfHeight(I,g){A.rawcolliderset_coSetHalfHeight(this.__wbg_ptr,I,g)}coRoundRadius(I){try{const B=A.__wbindgen_add_to_stack_pointer(-16);A.rawcolliderset_coRoundRadius(B,this.__wbg_ptr,I);var g=G()[B/4+0],C=K()[B/4+1];return 0===g?void 0:C}finally{A.__wbindgen_add_to_stack_pointer(16)}}coSetRoundRadius(I,g){A.rawcolliderset_coSetRoundRadius(this.__wbg_ptr,I,g)}coVertices(I){try{const B=A.__wbindgen_add_to_stack_pointer(-16);A.rawcolliderset_coVertices(B,this.__wbg_ptr,I);var g=G()[B/4+0],C=G()[B/4+1];let Q;return 0!==g&&(Q=N(g,C).slice(),A.__wbindgen_free(g,4*C,4)),Q}finally{A.__wbindgen_add_to_stack_pointer(16)}}coIndices(I){try{const B=A.__wbindgen_add_to_stack_pointer(-16);A.rawcolliderset_coIndices(B,this.__wbg_ptr,I);var g=G()[B/4+0],C=G()[B/4+1];let Q;return 0!==g&&(Q=function(A,I){return A>>>=0,M().subarray(A/4,A/4+I)}(g,C).slice(),A.__wbindgen_free(g,4*C,4)),Q}finally{A.__wbindgen_add_to_stack_pointer(16)}}coHeightfieldHeights(I){try{const B=A.__wbindgen_add_to_stack_pointer(-16);A.rawcolliderset_coHeightfieldHeights(B,this.__wbg_ptr,I);var g=G()[B/4+0],C=G()[B/4+1];let Q;return 0!==g&&(Q=N(g,C).slice(),A.__wbindgen_free(g,4*C,4)),Q}finally{A.__wbindgen_add_to_stack_pointer(16)}}coHeightfieldScale(I){const g=A.rawcolliderset_coHeightfieldScale(this.__wbg_ptr,I);return 0===g?void 0:DA.__wrap(g)}coHeightfieldNRows(I){try{const B=A.__wbindgen_add_to_stack_pointer(-16);A.rawcolliderset_coHeightfieldNRows(B,this.__wbg_ptr,I);var g=G()[B/4+0],C=G()[B/4+1];return 0===g?void 0:C>>>0}finally{A.__wbindgen_add_to_stack_pointer(16)}}coHeightfieldNCols(I){try{const B=A.__wbindgen_add_to_stack_pointer(-16);A.rawcolliderset_coHeightfieldNCols(B,this.__wbg_ptr,I);var g=G()[B/4+0],C=G()[B/4+1];return 0===g?void 0:C>>>0}finally{A.__wbindgen_add_to_stack_pointer(16)}}coParent(I){try{const B=A.__wbindgen_add_to_stack_pointer(-16);A.rawcolliderset_coParent(B,this.__wbg_ptr,I);var g=G()[B/4+0],C=D()[B/8+1];return 0===g?void 0:C}finally{A.__wbindgen_add_to_stack_pointer(16)}}coSetEnabled(I,g){A.rawcolliderset_coSetEnabled(this.__wbg_ptr,I,g)}coIsEnabled(I){return 0!==A.rawcolliderset_coIsEnabled(this.__wbg_ptr,I)}coFriction(I){return A.rawcolliderset_coFriction(this.__wbg_ptr,I)}coRestitution(I){return A.rawcolliderset_coRestitution(this.__wbg_ptr,I)}coDensity(I){return A.rawcolliderset_coDensity(this.__wbg_ptr,I)}coMass(I){return A.rawcolliderset_coMass(this.__wbg_ptr,I)}coVolume(I){return A.rawcolliderset_coVolume(this.__wbg_ptr,I)}coCollisionGroups(I){return A.rawcolliderset_coCollisionGroups(this.__wbg_ptr,I)>>>0}coSolverGroups(I){return A.rawcolliderset_coSolverGroups(this.__wbg_ptr,I)>>>0}coActiveHooks(I){return A.rawcolliderset_coActiveHooks(this.__wbg_ptr,I)>>>0}coActiveCollisionTypes(I){return A.rawcolliderset_coActiveCollisionTypes(this.__wbg_ptr,I)}coActiveEvents(I){return A.rawcolliderset_coActiveEvents(this.__wbg_ptr,I)>>>0}coContactForceEventThreshold(I){return A.rawcolliderset_coContactForceEventThreshold(this.__wbg_ptr,I)}coContainsPoint(I,g){a(g,DA);return 0!==A.rawcolliderset_coContainsPoint(this.__wbg_ptr,I,g.__wbg_ptr)}coCastShape(I,g,C,B,Q,E,i,D){a(g,DA),a(C,BA),a(B,DA),a(Q,IA),a(E,DA);const o=A.rawcolliderset_coCastShape(this.__wbg_ptr,I,g.__wbg_ptr,C.__wbg_ptr,B.__wbg_ptr,Q.__wbg_ptr,E.__wbg_ptr,i,D);return 0===o?void 0:iA.__wrap(o)}coCastCollider(I,g,C,B,Q,E){a(g,DA),a(B,DA);const i=A.rawcolliderset_coCastCollider(this.__wbg_ptr,I,g.__wbg_ptr,C,B.__wbg_ptr,Q,E);return 0===i?void 0:QA.__wrap(i)}coIntersectsShape(I,g,C,B){a(g,BA),a(C,DA),a(B,IA);return 0!==A.rawcolliderset_coIntersectsShape(this.__wbg_ptr,I,g.__wbg_ptr,C.__wbg_ptr,B.__wbg_ptr)}coContactShape(I,g,C,B,Q){a(g,BA),a(C,DA),a(B,IA);const E=A.rawcolliderset_coContactShape(this.__wbg_ptr,I,g.__wbg_ptr,C.__wbg_ptr,B.__wbg_ptr,Q);return 0===E?void 0:EA.__wrap(E)}coContactCollider(I,g,C){const B=A.rawcolliderset_coContactCollider(this.__wbg_ptr,I,g,C);return 0===B?void 0:EA.__wrap(B)}coProjectPoint(I,g,C){a(g,DA);const B=A.rawcolliderset_coProjectPoint(this.__wbg_ptr,I,g.__wbg_ptr,C);return u.__wrap(B)}coIntersectsRay(I,g,C,B){a(g,DA),a(C,DA);return 0!==A.rawcolliderset_coIntersectsRay(this.__wbg_ptr,I,g.__wbg_ptr,C.__wbg_ptr,B)}coCastRay(I,g,C,B,Q){a(g,DA),a(C,DA);return A.rawcolliderset_coCastRay(this.__wbg_ptr,I,g.__wbg_ptr,C.__wbg_ptr,B,Q)}coCastRayAndGetNormal(I,g,C,B,Q){a(g,DA),a(C,DA);const E=A.rawcolliderset_coCastRayAndGetNormal(this.__wbg_ptr,I,g.__wbg_ptr,C.__wbg_ptr,B,Q);return 0===E?void 0:$.__wrap(E)}coSetSensor(I,g){A.rawcolliderset_coSetSensor(this.__wbg_ptr,I,g)}coSetRestitution(I,g){A.rawcolliderset_coSetRestitution(this.__wbg_ptr,I,g)}coSetFriction(I,g){A.rawcolliderset_coSetFriction(this.__wbg_ptr,I,g)}coFrictionCombineRule(I){return A.rawcolliderset_coFrictionCombineRule(this.__wbg_ptr,I)>>>0}coSetFrictionCombineRule(I,g){A.rawcolliderset_coSetFrictionCombineRule(this.__wbg_ptr,I,g)}coRestitutionCombineRule(I){return A.rawcolliderset_coRestitutionCombineRule(this.__wbg_ptr,I)>>>0}coSetRestitutionCombineRule(I,g){A.rawcolliderset_coSetRestitutionCombineRule(this.__wbg_ptr,I,g)}coSetCollisionGroups(I,g){A.rawcolliderset_coSetCollisionGroups(this.__wbg_ptr,I,g)}coSetSolverGroups(I,g){A.rawcolliderset_coSetSolverGroups(this.__wbg_ptr,I,g)}coSetActiveHooks(I,g){A.rawcolliderset_coSetActiveHooks(this.__wbg_ptr,I,g)}coSetActiveEvents(I,g){A.rawcolliderset_coSetActiveEvents(this.__wbg_ptr,I,g)}coSetActiveCollisionTypes(I,g){A.rawcolliderset_coSetActiveCollisionTypes(this.__wbg_ptr,I,g)}coSetShape(I,g){a(g,BA),A.rawcolliderset_coSetShape(this.__wbg_ptr,I,g.__wbg_ptr)}coSetContactForceEventThreshold(I,g){A.rawcolliderset_coSetContactForceEventThreshold(this.__wbg_ptr,I,g)}coSetDensity(I,g){A.rawcolliderset_coSetDensity(this.__wbg_ptr,I,g)}coSetMass(I,g){A.rawcolliderset_coSetMass(this.__wbg_ptr,I,g)}coSetMassProperties(I,g,C,B,Q){a(C,DA),a(B,DA),a(Q,IA),A.rawcolliderset_coSetMassProperties(this.__wbg_ptr,I,g,C.__wbg_ptr,B.__wbg_ptr,Q.__wbg_ptr)}constructor(){const I=A.rawcolliderset_new();return this.__wbg_ptr=I>>>0,this}len(){return A.rawcolliderset_len(this.__wbg_ptr)>>>0}contains(I){return 0!==A.rawcolliderset_contains(this.__wbg_ptr,I)}createCollider(I,g,C,B,Q,E,i,o,w,S,k,h,K,U,J,N,y,M,F,q,R,s,c,Y){try{const L=A.__wbindgen_add_to_stack_pointer(-16);a(g,BA),a(C,DA),a(B,IA),a(i,DA),a(o,DA),a(w,IA),a(Y,AA),A.rawcolliderset_createCollider(L,this.__wbg_ptr,I,g.__wbg_ptr,C.__wbg_ptr,B.__wbg_ptr,Q,E,i.__wbg_ptr,o.__wbg_ptr,w.__wbg_ptr,S,k,h,K,U,J,N,y,M,F,q,R,s,c,Y.__wbg_ptr);var H=G()[L/4+0],l=D()[L/8+1];return 0===H?void 0:l}finally{A.__wbindgen_add_to_stack_pointer(16)}}remove(I,g,C,B){a(g,m),a(C,AA),A.rawcolliderset_remove(this.__wbg_ptr,I,g.__wbg_ptr,C.__wbg_ptr,B)}isHandleValid(I){return 0!==A.rawcolliderset_contains(this.__wbg_ptr,I)}forEachColliderHandle(g){try{A.rawcolliderset_forEachColliderHandle(this.__wbg_ptr,J(g))}finally{I[U++]=void 0}}}class e{static __wrap(A){A>>>=0;const I=Object.create(e.prototype);return I.__wbg_ptr=A,I}__destroy_into_raw(){const A=this.__wbg_ptr;return this.__wbg_ptr=0,A}free(){const I=this.__destroy_into_raw();A.__wbg_rawcontactforceevent_free(I)}collider1(){return A.rawcharactercollision_handle(this.__wbg_ptr)}collider2(){return A.rawcontactforceevent_collider2(this.__wbg_ptr)}total_force(){const I=A.rawcontactforceevent_total_force(this.__wbg_ptr);return DA.__wrap(I)}total_force_magnitude(){return A.rawcontactforceevent_total_force_magnitude(this.__wbg_ptr)}max_force_direction(){const I=A.rawcontactforceevent_max_force_direction(this.__wbg_ptr);return DA.__wrap(I)}max_force_magnitude(){return A.rawcontactforceevent_max_force_magnitude(this.__wbg_ptr)}}class r{static __wrap(A){A>>>=0;const I=Object.create(r.prototype);return I.__wbg_ptr=A,I}__destroy_into_raw(){const A=this.__wbg_ptr;return this.__wbg_ptr=0,A}free(){const I=this.__destroy_into_raw();A.__wbg_rawcontactmanifold_free(I)}normal(){const I=A.rawcontactmanifold_normal(this.__wbg_ptr);return DA.__wrap(I)}local_n1(){const I=A.rawcontactmanifold_local_n1(this.__wbg_ptr);return DA.__wrap(I)}local_n2(){const I=A.rawcontactmanifold_local_n2(this.__wbg_ptr);return DA.__wrap(I)}subshape1(){return A.rawcontactmanifold_subshape1(this.__wbg_ptr)>>>0}subshape2(){return A.rawcontactmanifold_subshape2(this.__wbg_ptr)>>>0}num_contacts(){return A.rawcontactmanifold_num_contacts(this.__wbg_ptr)>>>0}contact_local_p1(I){const g=A.rawcontactmanifold_contact_local_p1(this.__wbg_ptr,I);return 0===g?void 0:DA.__wrap(g)}contact_local_p2(I){const g=A.rawcontactmanifold_contact_local_p2(this.__wbg_ptr,I);return 0===g?void 0:DA.__wrap(g)}contact_dist(I){return A.rawcontactmanifold_contact_dist(this.__wbg_ptr,I)}contact_fid1(I){return A.rawcontactmanifold_contact_fid1(this.__wbg_ptr,I)>>>0}contact_fid2(I){return A.rawcontactmanifold_contact_fid2(this.__wbg_ptr,I)>>>0}contact_impulse(I){return A.rawcontactmanifold_contact_impulse(this.__wbg_ptr,I)}contact_tangent_impulse_x(I){return A.rawcontactmanifold_contact_tangent_impulse_x(this.__wbg_ptr,I)}contact_tangent_impulse_y(I){return A.rawcontactmanifold_contact_tangent_impulse_y(this.__wbg_ptr,I)}num_solver_contacts(){return A.rawcontactmanifold_num_solver_contacts(this.__wbg_ptr)>>>0}solver_contact_point(I){const g=A.rawcontactmanifold_solver_contact_point(this.__wbg_ptr,I);return 0===g?void 0:DA.__wrap(g)}solver_contact_dist(I){return A.rawcontactmanifold_solver_contact_dist(this.__wbg_ptr,I)}solver_contact_friction(I){return A.rawcontactmanifold_solver_contact_friction(this.__wbg_ptr,I)}solver_contact_restitution(I){return A.rawcontactmanifold_solver_contact_restitution(this.__wbg_ptr,I)}solver_contact_tangent_velocity(I){const g=A.rawcontactmanifold_solver_contact_tangent_velocity(this.__wbg_ptr,I);return DA.__wrap(g)}}class d{static __wrap(A){A>>>=0;const I=Object.create(d.prototype);return I.__wbg_ptr=A,I}__destroy_into_raw(){const A=this.__wbg_ptr;return this.__wbg_ptr=0,A}free(){const I=this.__destroy_into_raw();A.__wbg_rawcontactpair_free(I)}collider1(){return A.rawcontactpair_collider1(this.__wbg_ptr)}collider2(){return A.rawcontactpair_collider2(this.__wbg_ptr)}numContactManifolds(){return A.rawcontactpair_numContactManifolds(this.__wbg_ptr)>>>0}contactManifold(I){const g=A.rawcontactpair_contactManifold(this.__wbg_ptr,I);return 0===g?void 0:r.__wrap(g)}}class T{__destroy_into_raw(){const A=this.__wbg_ptr;return this.__wbg_ptr=0,A}free(){const I=this.__destroy_into_raw();A.__wbg_rawdebugrenderpipeline_free(I)}constructor(){const I=A.rawdebugrenderpipeline_new();return this.__wbg_ptr=I>>>0,this}vertices(){return Q(A.rawdebugrenderpipeline_vertices(this.__wbg_ptr))}colors(){return Q(A.rawdebugrenderpipeline_colors(this.__wbg_ptr))}render(I,g,C,B,Q){a(I,AA),a(g,p),a(C,W),a(B,f),a(Q,X),A.rawdebugrenderpipeline_render(this.__wbg_ptr,I.__wbg_ptr,g.__wbg_ptr,C.__wbg_ptr,B.__wbg_ptr,Q.__wbg_ptr)}}class n{static __wrap(A){A>>>=0;const I=Object.create(n.prototype);return I.__wbg_ptr=A,I}__destroy_into_raw(){const A=this.__wbg_ptr;return this.__wbg_ptr=0,A}free(){const I=this.__destroy_into_raw();A.__wbg_rawdeserializedworld_free(I)}takeGravity(){const I=A.rawdeserializedworld_takeGravity(this.__wbg_ptr);return 0===I?void 0:DA.__wrap(I)}takeIntegrationParameters(){const I=A.rawdeserializedworld_takeIntegrationParameters(this.__wbg_ptr);return 0===I?void 0:x.__wrap(I)}takeIslandManager(){const I=A.rawdeserializedworld_takeIslandManager(this.__wbg_ptr);return 0===I?void 0:m.__wrap(I)}takeBroadPhase(){const I=A.rawdeserializedworld_takeBroadPhase(this.__wbg_ptr);return 0===I?void 0:l.__wrap(I)}takeNarrowPhase(){const I=A.rawdeserializedworld_takeNarrowPhase(this.__wbg_ptr);return 0===I?void 0:X.__wrap(I)}takeBodies(){const I=A.rawdeserializedworld_takeBodies(this.__wbg_ptr);return 0===I?void 0:AA.__wrap(I)}takeColliders(){const I=A.rawdeserializedworld_takeColliders(this.__wbg_ptr);return 0===I?void 0:p.__wrap(I)}takeImpulseJoints(){const I=A.rawdeserializedworld_takeImpulseJoints(this.__wbg_ptr);return 0===I?void 0:W.__wrap(I)}takeMultibodyJoints(){const I=A.rawdeserializedworld_takeMultibodyJoints(this.__wbg_ptr);return 0===I?void 0:f.__wrap(I)}}class O{__destroy_into_raw(){const A=this.__wbg_ptr;return this.__wbg_ptr=0,A}free(){const I=this.__destroy_into_raw();A.__wbg_rawdynamicraycastvehiclecontroller_free(I)}constructor(I){const g=A.rawdynamicraycastvehiclecontroller_new(I);return this.__wbg_ptr=g>>>0,this}current_vehicle_speed(){return A.rawdynamicraycastvehiclecontroller_current_vehicle_speed(this.__wbg_ptr)}chassis(){return A.rawdynamicraycastvehiclecontroller_chassis(this.__wbg_ptr)}index_up_axis(){return A.rawdynamicraycastvehiclecontroller_index_up_axis(this.__wbg_ptr)>>>0}set_index_up_axis(I){A.rawdynamicraycastvehiclecontroller_set_index_up_axis(this.__wbg_ptr,I)}index_forward_axis(){return A.rawdynamicraycastvehiclecontroller_index_forward_axis(this.__wbg_ptr)>>>0}set_index_forward_axis(I){A.rawdynamicraycastvehiclecontroller_set_index_forward_axis(this.__wbg_ptr,I)}add_wheel(I,g,C,B,Q){a(I,DA),a(g,DA),a(C,DA),A.rawdynamicraycastvehiclecontroller_add_wheel(this.__wbg_ptr,I.__wbg_ptr,g.__wbg_ptr,C.__wbg_ptr,B,Q)}num_wheels(){return A.rawdynamicraycastvehiclecontroller_num_wheels(this.__wbg_ptr)>>>0}update_vehicle(g,C,B,Q,i,D,o){try{a(C,AA),a(B,p),a(Q,z),A.rawdynamicraycastvehiclecontroller_update_vehicle(this.__wbg_ptr,g,C.__wbg_ptr,B.__wbg_ptr,Q.__wbg_ptr,i,!E(D),E(D)?0:D,J(o))}finally{I[U++]=void 0}}wheel_chassis_connection_point_cs(I){const g=A.rawdynamicraycastvehiclecontroller_wheel_chassis_connection_point_cs(this.__wbg_ptr,I);return 0===g?void 0:DA.__wrap(g)}set_wheel_chassis_connection_point_cs(I,g){a(g,DA),A.rawdynamicraycastvehiclecontroller_set_wheel_chassis_connection_point_cs(this.__wbg_ptr,I,g.__wbg_ptr)}wheel_suspension_rest_length(I){try{const B=A.__wbindgen_add_to_stack_pointer(-16);A.rawdynamicraycastvehiclecontroller_wheel_suspension_rest_length(B,this.__wbg_ptr,I);var g=G()[B/4+0],C=K()[B/4+1];return 0===g?void 0:C}finally{A.__wbindgen_add_to_stack_pointer(16)}}set_wheel_suspension_rest_length(I,g){A.rawdynamicraycastvehiclecontroller_set_wheel_suspension_rest_length(this.__wbg_ptr,I,g)}wheel_max_suspension_travel(I){try{const B=A.__wbindgen_add_to_stack_pointer(-16);A.rawdynamicraycastvehiclecontroller_wheel_max_suspension_travel(B,this.__wbg_ptr,I);var g=G()[B/4+0],C=K()[B/4+1];return 0===g?void 0:C}finally{A.__wbindgen_add_to_stack_pointer(16)}}set_wheel_max_suspension_travel(I,g){A.rawdynamicraycastvehiclecontroller_set_wheel_max_suspension_travel(this.__wbg_ptr,I,g)}wheel_radius(I){try{const B=A.__wbindgen_add_to_stack_pointer(-16);A.rawdynamicraycastvehiclecontroller_wheel_radius(B,this.__wbg_ptr,I);var g=G()[B/4+0],C=K()[B/4+1];return 0===g?void 0:C}finally{A.__wbindgen_add_to_stack_pointer(16)}}set_wheel_radius(I,g){A.rawdynamicraycastvehiclecontroller_set_wheel_radius(this.__wbg_ptr,I,g)}wheel_suspension_stiffness(I){try{const B=A.__wbindgen_add_to_stack_pointer(-16);A.rawdynamicraycastvehiclecontroller_wheel_suspension_stiffness(B,this.__wbg_ptr,I);var g=G()[B/4+0],C=K()[B/4+1];return 0===g?void 0:C}finally{A.__wbindgen_add_to_stack_pointer(16)}}set_wheel_suspension_stiffness(I,g){A.rawdynamicraycastvehiclecontroller_set_wheel_suspension_stiffness(this.__wbg_ptr,I,g)}wheel_suspension_compression(I){try{const B=A.__wbindgen_add_to_stack_pointer(-16);A.rawdynamicraycastvehiclecontroller_wheel_suspension_compression(B,this.__wbg_ptr,I);var g=G()[B/4+0],C=K()[B/4+1];return 0===g?void 0:C}finally{A.__wbindgen_add_to_stack_pointer(16)}}set_wheel_suspension_compression(I,g){A.rawdynamicraycastvehiclecontroller_set_wheel_suspension_compression(this.__wbg_ptr,I,g)}wheel_suspension_relaxation(I){try{const B=A.__wbindgen_add_to_stack_pointer(-16);A.rawdynamicraycastvehiclecontroller_wheel_suspension_relaxation(B,this.__wbg_ptr,I);var g=G()[B/4+0],C=K()[B/4+1];return 0===g?void 0:C}finally{A.__wbindgen_add_to_stack_pointer(16)}}set_wheel_suspension_relaxation(I,g){A.rawdynamicraycastvehiclecontroller_set_wheel_suspension_relaxation(this.__wbg_ptr,I,g)}wheel_max_suspension_force(I){try{const B=A.__wbindgen_add_to_stack_pointer(-16);A.rawdynamicraycastvehiclecontroller_wheel_max_suspension_force(B,this.__