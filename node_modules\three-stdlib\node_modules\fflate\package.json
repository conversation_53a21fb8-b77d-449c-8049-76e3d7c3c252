{"name": "fflate", "version": "0.6.10", "description": "High performance (de)compression in an 8kB package", "main": "./lib/index.cjs", "module": "./esm/browser.js", "types": "./lib/index.d.ts", "unpkg": "./umd/index.js", "jsdelivr": "./umd/index.js", "browser": {"./lib/node-worker.cjs": "./lib/worker.cjs"}, "exports": {".": {"node": {"import": "./esm/index.mjs", "require": "./lib/node.cjs"}, "import": "./esm/browser.js", "require": "./lib/index.cjs"}, "./node": {"import": "./esm/index.mjs", "require": "./lib/node.cjs"}, "./browser": {"import": "./esm/browser.js", "require": "./lib/browser.cjs"}}, "targets": {"main": false, "module": false, "browser": false, "types": false}, "sideEffects": false, "homepage": "https://101arrowz.github.io/fflate", "repository": "https://github.com/101arrowz/fflate", "bugs": {"email": "<EMAIL>", "url": "https://github.com/101arrowz/fflate/issues"}, "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "license": "MIT", "keywords": ["gzip", "gunzip", "deflate", "inflate", "compression", "decompression", "zlib", "pako", "j<PERSON><PERSON>", "browser", "node.js", "tiny", "fast", "zip", "unzip", "non-blocking"], "scripts": {"build": "yarn build:lib && yarn build:docs && yarn build:demo", "script": "node -r ts-node/register scripts/$SC.ts", "build:lib": "tsc && tsc --project tsconfig.esm.json && yarn build:rewrite && yarn build:umd", "build:umd": "SC=buildUMD yarn script", "build:rewrite": "SC=rewriteBuilds yarn script", "build:demo": "tsc --project tsconfig.demo.json && parcel build demo/index.html --public-url \"./\" && SC=cpGHPages yarn script", "build:docs": "typedoc --mode library --plugin typedoc-plugin-markdown --hideProjectName --hideBreadcrumbs --readme none --disableSources --excludePrivate --excludeProtected --out docs/ src/index.ts", "test": "TS_NODE_PROJECT=test/tsconfig.json uvu -b -r ts-node/register test", "prepack": "yarn build && yarn test"}, "devDependencies": {"@types/node": "^14.11.2", "@types/pako": "*", "@types/react": "^16.9.55", "@types/react-dom": "^16.9.9", "jszip": "^3.5.0", "pako": "*", "parcel": "^2.0.0-nightly.440", "parcel-config-precache-manifest": "^0.0.3", "preact": "^10.5.5", "react": "^17.0.1", "react-dom": "^17.0.1", "simple-git": "^2.22.0", "terser": "^5.3.8", "tiny-inflate": "*", "ts-node": "^9.0.0", "typedoc": "^0.17.0-3", "typedoc-plugin-markdown": "^3.0.2", "typescript": "^4.0.2", "uvu": "^0.3.3", "uzip": "*"}, "alias": {"react": "preact/compat", "react-dom": "preact/compat", "react-dom/test-utils": "preact/test-utils"}}