/**
 * @monogrid/gainmap-js v3.1.0
 * With ❤️, by MONOGRID <<EMAIL>>
 */

var commonjsGlobal = typeof globalThis !== 'undefined' ? globalThis : typeof window !== 'undefined' ? window : typeof global !== 'undefined' ? global : typeof self !== 'undefined' ? self : {};

function getDefaultExportFromCjs (x) {
	return x && x.__esModule && Object.prototype.hasOwnProperty.call(x, 'default') ? x['default'] : x;
}

export { commonjsGlobal as c, getDefaultExportFromCjs as g };
