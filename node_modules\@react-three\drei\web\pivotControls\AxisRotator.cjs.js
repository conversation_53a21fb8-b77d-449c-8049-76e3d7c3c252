"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("react"),t=require("three"),r=require("@react-three/fiber"),n=require("../../core/Line.cjs.js"),o=require("../Html.cjs.js"),a=require("./context.cjs.js");function i(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}require("@babel/runtime/helpers/extends"),require("three-stdlib"),require("react-dom/client");var c=i(e),s=i(t);const l=new s.Vector3,u=new s.Vector3,d=e=>180*e/Math.PI,p=e=>{let t=((e,t)=>{let r=Math.floor(e/t);return r=r<0?r+1:r,e-r*t})(e,2*Math.PI);return Math.abs(t)<1e-6?0:(t<0&&(t+=2*Math.PI),t)},m=new s.Matrix4,h=new s.Vector3,x=new s.Ray,P=new s.Vector3;exports.AxisRotator=({dir1:e,dir2:t,axis:i})=>{const{rotationLimits:M,annotations:f,annotationsClass:b,depthTest:g,scale:y,lineWidth:w,fixed:v,axisColors:C,hoveredColor:j,opacity:I,onDragStart:k,onDrag:O,onDragEnd:R,userData:q}=c.useContext(a.context),V=r.useThree((e=>e.controls)),D=c.useRef(null),F=c.useRef(null),E=c.useRef(0),W=c.useRef(0),z=c.useRef(null),[T,A]=c.useState(!1),L=c.useCallback((e=>{f&&(D.current.innerText=`${d(W.current).toFixed(0)}º`,D.current.style.display="block"),e.stopPropagation();const t=e.point.clone(),r=(new s.Vector3).setFromMatrixPosition(F.current.matrixWorld),n=(new s.Vector3).setFromMatrixColumn(F.current.matrixWorld,0).normalize(),o=(new s.Vector3).setFromMatrixColumn(F.current.matrixWorld,1).normalize(),a=(new s.Vector3).setFromMatrixColumn(F.current.matrixWorld,2).normalize(),c=(new s.Plane).setFromNormalAndCoplanarPoint(a,r);z.current={clickPoint:t,origin:r,e1:n,e2:o,normal:a,plane:c},k({component:"Rotator",axis:i,origin:r,directions:[n,o,a]}),V&&(V.enabled=!1),e.target.setPointerCapture(e.pointerId)}),[f,V,k,i]),S=c.useCallback((e=>{if(e.stopPropagation(),T||A(!0),z.current){const{clickPoint:t,origin:r,e1:n,e2:o,normal:a,plane:c}=z.current,[b,g]=(null==M?void 0:M[i])||[void 0,void 0];x.copy(e.ray),x.intersectPlane(c,P),x.direction.negate(),x.intersectPlane(c,P);let y=((e,t,r,n,o)=>{l.copy(e).sub(r),u.copy(t).sub(r);const a=n.dot(n),i=o.dot(o),c=l.dot(n)/a,s=l.dot(o)/i,d=u.dot(n)/a,p=u.dot(o)/i,m=Math.atan2(s,c);return Math.atan2(p,d)-m})(t,P,r,n,o),w=d(y);e.shiftKey&&(w=10*Math.round(w/10),y=(e=>e*Math.PI/180)(w)),void 0!==b&&void 0!==g&&g-b<2*Math.PI?(y=p(y),y=y>Math.PI?y-2*Math.PI:y,y=s.MathUtils.clamp(y,b-E.current,g-E.current),W.current=E.current+y):(W.current=p(E.current+y),W.current=W.current>Math.PI?W.current-2*Math.PI:W.current),f&&(w=d(W.current),D.current.innerText=`${w.toFixed(0)}º`),m.makeRotationAxis(a,y),h.copy(r).applyMatrix4(m).sub(r).negate(),m.setPosition(h),O(m)}}),[f,O,T,M,i]),_=c.useCallback((e=>{f&&(D.current.style.display="none"),e.stopPropagation(),E.current=W.current,z.current=null,R(),V&&(V.enabled=!0),e.target.releasePointerCapture(e.pointerId)}),[f,V,R]),U=c.useCallback((e=>{e.stopPropagation(),A(!1)}),[]),H=c.useMemo((()=>{const r=e.clone().normalize(),n=t.clone().normalize();return(new s.Matrix4).makeBasis(r,n,r.clone().cross(n))}),[e,t]),N=v?.65:.65*y,$=c.useMemo((()=>{const e=[];for(let t=0;t<=32;t++){const r=t*(Math.PI/2)/32;e.push(new s.Vector3(Math.cos(r)*N,Math.sin(r)*N,0))}return e}),[N]);return c.createElement("group",{ref:F,onPointerDown:L,onPointerMove:S,onPointerUp:_,onPointerOut:U,matrix:H,matrixAutoUpdate:!1},f&&c.createElement(o.Html,{position:[N,N,0]},c.createElement("div",{style:{display:"none",background:"#151520",color:"white",padding:"6px 8px",borderRadius:7,whiteSpace:"nowrap"},className:b,ref:D})),c.createElement(n.Line,{points:$,lineWidth:4*w,visible:!1,userData:q}),c.createElement(n.Line,{transparent:!0,raycast:()=>null,depthTest:g,points:$,lineWidth:w,side:s.DoubleSide,color:T?j:C[i],opacity:I,polygonOffset:!0,polygonOffsetFactor:-10,fog:!1}))};
