"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("react"),r=require("@react-three/fiber");function t(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var n=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,n.get?n:{enumerable:!0,get:function(){return e[t]}})}})),r.default=e,Object.freeze(r)}var n=t(e);exports.AdaptiveEvents=function(){const e=r.useThree((e=>e.get)),t=r.useThree((e=>e.setEvents)),u=r.useThree((e=>e.performance.current));return n.useEffect((()=>{const r=e().events.enabled;return()=>t({enabled:r})}),[]),n.useEffect((()=>t({enabled:1===u})),[u]),null};
